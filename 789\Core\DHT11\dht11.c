#include "dht11.h"
//uint16_t temperature;
//uint16_t humidity;
//char temp,humi;

 uint8_t DHT11_Read_Data(uint16_t *temp,uint16_t *humi)    
{        
 	uint8_t buf[5];
	uint8_t i;
	DHT11_SendOut();
	if(DHT11_Check()==0)
	{
		for(i=0;i<5;i++)
		{
			buf[i]=DHT11_ReadByte();
		}
		if((buf[0]+buf[1]+buf[2]+buf[3])==buf[4])
		{
			*humi=(buf[0]<<8) + buf[1];
			*temp=(buf[2]<<8) + buf[3];
		}
	}else return 1;
	return 0;	    
}

 

void DHT11_SendOut(void)
{
	DHT11_IO_OUT();
	DHT11_Date_Low;
	HAL_Delay(18);  
	DHT11_Date_High;
	delay_us(30);   
}
 

uint8_t DHT11_Check(void)
{   
	uint8_t retry=0;
	DHT11_IO_IN();      
	while (DHT11_Date && retry<100){	
		retry++;
		delay_us(1);
	}
	if(retry>=100)return 1;
	else retry=0;
	while (!DHT11_Date && retry<100){ 
		retry++;
		delay_us(1);
	}
	if(retry>=100)return 1;
	return 0;
}
 

uint8_t DH11_ReadBit(void)
{
    uint8_t retry=0;    
		while(DHT11_Date && retry<100){
		delay_us(1);   
		retry++;
	}
	retry=0;
  
	while(!DHT11_Date && retry<100){
		delay_us(1);  
		retry++;
	}
	delay_us(40);	
	if(DHT11_Date)return 1;
	else return 0;
}
 

uint8_t DHT11_ReadByte(void)
{
	uint8_t i;
	uint8_t Read_Byte=0;
	for(i=0;i<8;i++){
   	Read_Byte<<=1;     
	  Read_Byte |= DH11_ReadBit(); 
  }
	return Read_Byte;
}
 

void DHT11_IO_IN(void)
{	
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.Pin = GPIO_PIN_6;
	GPIO_InitStructure.Mode = GPIO_MODE_INPUT;
	HAL_GPIO_Init(GPIOD,&GPIO_InitStructure);
}

void DHT11_IO_OUT(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.Pin = GPIO_PIN_6;
	GPIO_InitStructure.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStructure.Speed = GPIO_SPEED_FREQ_HIGH;
	HAL_GPIO_Init(GPIOD,&GPIO_InitStructure);
}

void delay_us(uint16_t us)
{
	uint16_t differ = 0xffff-us-5;				
	__HAL_TIM_SET_COUNTER(&htim3,differ);	
	HAL_TIM_Base_Start(&htim3);			
	
	while(differ < 0xffff-5){	
		differ = __HAL_TIM_GET_COUNTER(&htim3);		
	}
	HAL_TIM_Base_Stop(&htim3);
}




uint8_t DHT11_Init(void)
{ 
  DHT11_SendOut();
	return DHT11_Check();
}
