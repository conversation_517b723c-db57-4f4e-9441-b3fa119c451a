#include "SGP30.h"


void SGP30_GPIO_Init(void)
{
  GPIO_InitTypeDef GPIO_InitStructure;

  RCC_APB2PeriphClockCmd(SGP30_SCL_GPIO_CLK | SGP30_SDA_GPIO_SDA, ENABLE);

  GPIO_InitStructure.GPIO_Pin = SGP30_SCL_GPIO_PIN;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
  GPIO_Init(SGP30_SCL_GPIO_PORT, &GPIO_InitStructure);

  GPIO_InitStructure.GPIO_Pin = SGP30_SDA_GPIO_PIN;
  GPIO_Init(SGP30_SDA_GPIO_PORT, &GPIO_InitStructure);
}

void SDA_OUT(void)
{
  GPIO_InitTypeDef GPIO_InitStructure;
  GPIO_InitStructure.GPIO_Pin = SGP30_SDA_GPIO_PIN;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
  GPIO_Init(SGP30_SDA_GPIO_PORT, &GPIO_InitStructure);
}

void SDA_IN(void)
{
  GPIO_InitTypeDef GPIO_InitStructure;
  GPIO_InitStructure.GPIO_Pin = SGP30_SDA_GPIO_PIN;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_10MHz;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
  GPIO_Init(SGP30_SDA_GPIO_PORT, &GPIO_InitStructure);
}

/****

*******I2C总线启动信号

*****/

void SGP30_IIC_Start(void)
{

   SGP30_SDA = 1;

   SGP30_SCL = 1;

   SGP30_IIC_Delay(DELAY_TIME);

   SGP30_SDA = 0;

   SGP30_IIC_Delay(DELAY_TIME);

   SGP30_SCL = 0;

}

 

/****

*******I2C总线停止信号

*****/

void SGP30_IIC_Stop(void)

{

   SGP30_SDA = 0;

   SGP30_SCL = 1;

   SGP30_IIC_Delay(DELAY_TIME);

   SGP30_SDA = 1;

   SGP30_IIC_Delay(DELAY_TIME);

}
/****

*******发送应答或非应答信号

*****/

void SGP30_IIC_SendAck(bit ackbit)

{

   SGP30_SCL = 0;

   SGP30_SDA = ackbit;

   SGP30_IIC_Delay(DELAY_TIME);

   SGP30_SCL = 1;

   SGP30_IIC_Delay(DELAY_TIME);

   SGP30_SCL = 0;

   SGP30_SDA = 1;

   SGP30_IIC_Delay(DELAY_TIME);

}

 

/****

*******等待应答信号

*****/

bit SGP30_IIC_WaitAck(void)

{

   bit ackbit;

   SGP30_SDA = 1;

   SGP30_SCL = 1;

   SGP30_IIC_Delay(DELAY_TIME);

   ackbit = SGP30_SDA;

   SGP30_SCL = 0;

   SGP30_IIC_Delay(DELAY_TIME);

   return ackbit;

}/****

*******I2C总线发送一个字节数据

*****/

void SGP30_IIC_SendByte(uchar byte)

{

   uchar i;

   SGP30_SCL = 0;

   for(i=0; i<8; i++)

   {

      if(byte & 0x80)

         SGP30_SDA = 1;

      else

         SGP30_SDA = 0;

      SGP30_IIC_Delay(DELAY_TIME);

      SGP30_SCL = 1;

      SGP30_IIC_Delay(DELAY_TIME);

      SGP30_SCL = 0;

      SGP30_IIC_Delay(DELAY_TIME);

      byte <<= 1;

   }

   SGP30_IIC_WaitAck();

}
/****

*******I2C总线接收一个字节数据

*****/

uchar SGP30_IIC_RecByte(bit ackbit)

{

   uchar i, dat;

   SGP30_SDA = 1;

   for(i=0; i<8; i++)

   {

      SGP30_SCL = 1;

      SGP30_IIC_Delay(DELAY_TIME);

      dat <<= 1;

      if(SGP30_SDA)

         dat |= 1;

      SGP30_SCL = 0;

      SGP30_IIC_Delay(DELAY_TIME);

   }

   SGP30_IIC_SendAck(ackbit);

   return dat;

}
/****

*******向SGP30的地址address中写入一个字节的数据

*****/

void SGP30_Write_Data(uchar address,uchar dat)

{

   SGP30_IIC_Start();

   SGP30_IIC_SendByte(SGP30_SlaveAddress);

   SGP30_IIC_SendByte(address);

   SGP30_IIC_SendByte(dat);

   SGP30_IIC_Stop();

}

 

/****

*******从SGP30的地址address中读取一个字节的数据

*****/

ulong SGP30_Read_Data()

{

   ulong dat;

   uint crc;

   SGP30_IIC_Start();

   SGP30_IIC_SendByte(SGP30_SlaveAddress + 1);

   dat = SGP30_IIC_RecByte(SGP30_ACK);               //CO2高位数据

   dat <<= 8;

   dat += SGP30_IIC_RecByte(SGP30_ACK);              //CO2低位数据

   crc = SGP30_IIC_RecByte(SGP30_ACK);                //CRC校验值

   dat <<= 8;

   dat += SGP30_IIC_RecByte(SGP30_ACK);              //TVOC高位数据

   dat <<= 8;

   dat += SGP30_IIC_RecByte(SGP30_ACK);              //TVOC低位数据

   crc = SGP30_IIC_RecByte(SGP30_NACK);               //CRC校验值

   SGP30_IIC_Stop();

   return(dat);

}

 

/****

*******SGP30初始化函数

*****/

void SGP30_Init(void)

{

   SGP30_Write_Data(0x20,0x03);

}

 

/****

*******SGP30获取CO2和TVOC值函数

*****/

void SGP30_Get_Co2_Tvoc_Value(uint *co2_value, uint *tvoc_value)

{

   ulong sgp30_value;

   SGP30_Write_Data(0x20,0x08);

   sgp30_value = SGP30_Read_Data();

   *co2_value = (sgp30_value & 0xffff0000) >> 16;

   *tvoc_value = sgp30_value & 0x0000ffff;

}
