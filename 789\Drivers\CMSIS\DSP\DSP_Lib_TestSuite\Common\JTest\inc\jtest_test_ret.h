#ifndef _JTEST_TEST_RET_H_
#define _JTEST_TEST_RET_H_

/*--------------------------------------------------------------------------------*/
/* Type Definitions */
/*--------------------------------------------------------------------------------*/

/**
 *  Values a #JTEST_TEST_t can return.
 */
typedef enum JTEST_TEST_RET_enum
{
    JTEST_TEST_PASSED,
    JTEST_TEST_FAILED
} JTEST_TEST_RET_t;

#endif /* _JTEST_TEST_RET_H_ */
