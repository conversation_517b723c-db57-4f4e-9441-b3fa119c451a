#include "sgp30.h"
#include "i2c.h"
//#include "utils.h"
//#include "SEGGER_RTT.h"

#define SGP30_ADDR (0x58<<1)//write addr
#define SGP30_ADDR_READ (0xb1)//read addr

//init sensor
HAL_StatusTypeDef sgp30_init()
{
    uint8_t data[2];

    //iaq init
    data[0]=(0x2003)>>8;
    data[1]=(0x2003) & 0x00FF;
    return HAL_I2C_Master_Transmit(&hi2c1,SGP30_ADDR,data,2,50);
}

//get data sample
HAL_StatusTypeDef sgp30_sample(float *co2, float *tvoc)
{
    uint8_t data[2];
    uint8_t readbuff[6];

    //measure iaq
    data[0]=(0x2008)>>8;
    data[1]=(0x2008) & 0x00FF;

    if(HAL_I2C_Master_Transmit(&hi2c1,SGP30_ADDR,data,2,50) != HAL_OK){
//        SEGGER_RTT_printf(0, "write cmd m_iaq error\r\n");
        return HAL_ERROR;
    }

    //waite data ok
    int timeout = 10;
    while(1){
        if(HAL_I2C_Master_Receive(&hi2c1,SGP30_ADDR_READ,readbuff,6,50) != HAL_OK){
//          SEGGER_RTT_printf(0, "recv data error\r\n");
            timeout--;
            HAL_Delay(5);
        }else{
            break;
        }

        if(timeout<=0){
                return HAL_ERROR;
//              break;
        }
    }

    //check crc
//    if(CheckCrc8(readbuff, 0xFF) != readbuff[2]  CheckCrc8(&readbuff[3], 0xFF) != readbuff[5])
//  {
//        return HAL_ERROR;
//    }

    *co2 = (readbuff[0]<<8)|(readbuff[1]);
    *tvoc = (readbuff[3]<<8)|(readbuff[4]);

    return HAL_OK;
}
