#ifndef __DHT11_H
#define __DHT11_H
#include "stm32f4xx_hal.h"
#include "main.h"
#include "tim.h"
#include "gpio.h"
 
#define   DHT11_Date         HAL_GPIO_ReadPin(GPIOD,GPIO_PIN_6)
#define   DHT11_Date_Low     HAL_GPIO_WritePin(GPIOD,GPIO_PIN_6,GPIO_PIN_RESET)
#define   DHT11_Date_High    HAL_GPIO_WritePin(GPIOD,GPIO_PIN_6,GPIO_PIN_SET)
 
//extern uint16_t temperature;
//extern uint16_t humidity;
//extern char temp,humi;
extern TIM_HandleTypeDef htim3; 

uint8_t DHT11_Init(void);
void DHT11_IO_IN(void);
void DHT11_IO_OUT(void);
void delay_us(uint16_t us);
void DHT11_SendOut(void);
uint8_t DHT11_Check(void);
uint8_t DH11_ReadBit(void);
uint8_t DHT11_ReadByte(void);
void DH11_ReadDate_Handle(void);
uint8_t DHT11_Read_Data(uint16_t *temp,uint16_t *humi);
 
#endif
